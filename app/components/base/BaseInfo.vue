<template>
  <div class="mt-2 grid grid-cols-12 gap-2">
    <div
      v-for="(row, index) in rows"
      :key="index"
      :class="row.key === 'uuid' ? 'col-span-12' : 'col-span-12 sm:col-span-6'"
    >
      <div class="text-[10px] font-light">
        <USkeleton
          v-if="row.loading"
          class="h-4 w-20"
        />
        <div v-else>
          {{ $t(row.key || "None") }}
        </div>
      </div>
      <div class="text-xs">
        <USkeleton
          v-if="row.loading"
          class="h-6 w-30 mt-2"
        />
        <div
          v-else-if="row.value"
          :class="row.key === 'uuid' ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 p-2 -ml-2 rounded transition-colors font-mono text-[10px] break-all' : ''"
          :title="row.key === 'uuid' ? $t('Click to copy') : ''"
          @click="row.key === 'uuid' ? copyToClipboard(row.value) : null"
        >
          {{ row.value }}
        </div>
        <div
          v-else
          class="font-thin italic"
        >
          {{ $t("None") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  properties: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()

const rows = computed(() => {
  if (props.loading) {
    return Array(6).fill({
      loading: true
    })
  }
  // Filter out properties that have no value (null, undefined, empty string)
  return Object.keys(props.properties)
    .filter((key) => {
      const value = props.properties[key]
      return value !== null && value !== undefined && value !== ''
    })
    .map(key => ({
      key,
      value: props.properties[key]
    }))
})

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // Show success notification
    const toast = useToast()
    toast.add({
      title: t('Copied to clipboard'),
      description: t('UUID has been copied to clipboard'),
      color: 'success'
    })
  } catch (err) {
    console.error('Failed to copy: ', err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)

    const toast = useToast()
    toast.add({
      title: t('Copied to clipboard'),
      description: t('UUID has been copied to clipboard'),
      color: 'success'
    })
  }
}
</script>
