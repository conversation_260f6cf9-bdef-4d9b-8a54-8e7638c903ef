<template>
  <div class="relative group">
    <div
      class="group-hover:hidden absolute top-2 right-2 flex flex-col items-end gap-1"
    >
      <span
        class="bg-gray-500/80 text-white text-[0.625rem] px-1.5 py-0.5 rounded z-10 text-xs font-semibold"
      >
        {{ $t(type || "") }}
      </span>
      <span
        v-if="style"
        class="bg-yellow-500/80 text-white text-[0.625rem] px-1.5 py-0.5 rounded z-10 text-xs font-semibold"
      >
        {{ style }}
      </span>
      <span
        v-if="status === 3"
        class="bg-red-500/90 text-white text-[0.625rem] px-1.5 py-0.5 rounded z-10 text-xs font-semibold flex items-center gap-1"
      >
        <UIcon
          name="lucide:alert-circle"
          class="w-3 h-3"
        />
        {{ $t("error") }}
      </span>
    </div>

    <!-- Extend <PERSON><PERSON> (visible on hover) -->
    <div
      class="absolute bottom-2 right-2 z-20"
    >
      <UDropdownMenu
        :items="menuItems"
        :ui="{
          content: 'w-32'
        }"
      >
        <UButton
          icon="i-lucide-more-vertical"
          color="neutral"
          variant="ghost"
          size="xs"
        />
      </UDropdownMenu>
    </div>

    <slot />
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
const toast = useToast()
const historyStore = useHistoryStore()
const { openConfirm } = useConfirm()

const props = defineProps({
  type: {
    type: String,
    required: true
  },
  style: {
    type: String,
    default: ''
  },
  status: {
    type: Number,
    default: 1
  },
  uuid: {
    type: String,
    required: true
  }
})

// Menu items for dropdown
const menuItems = computed(() => [
  [
    {
      label: t('delete') || 'Delete',
      icon: 'i-lucide-trash-2',
      color: 'red',
      onSelect: handleDelete
    }
  ]
])

// Handle delete action
const handleDelete = () => {
  openConfirm({
    title: t('confirmDelete') || 'Confirm Delete',
    description: t('confirmDeleteDescription') || 'Are you sure you want to delete this item? This action cannot be undone.',
    icon: 'i-lucide-trash-2',
    confirmText: t('delete') || 'Delete',
    cancelText: t('cancel') || 'Cancel',
    onConfirm: async () => {
      try {
        const result = await historyStore.deleteHistory(props.uuid)
        if (result !== null) {
          toast.add({
            title: t('success.deleted') || 'Success',
            description: t('historyDeleted') || 'History item deleted successfully',
            color: 'success'
          })
        } else {
          toast.add({
            title: t('error.general') || 'Error',
            description: historyStore.errors.deleteHistory?.message || t('deleteError') || 'Failed to delete history item',
            color: 'error'
          })
        }
      } catch (error) {
        toast.add({
          title: t('error.general') || 'Error',
          description: t('deleteError') || 'Failed to delete history item',
          color: 'error'
        })
      }
    }
  })
}
</script>
